# ⚡ Kilat.js

> Revolutionary fullstack framework based on Bun.js with monorepo structure, plugin-first architecture, and exceptional developer experience.

## 🚀 Features

- **⚡ Speedrun Runtime**: Powered by Bun.js with Node.js fallback
- **🏗️ Monorepo Architecture**: Semi-modular structure for scalability
- **🔌 Plugin-First**: Extensible plugin system like Vite/Next.js
- **🎨 Built-in UI**: Modern components with multiple themes
- **📱 SSR/CSR/SSG**: Full rendering support
- **🛡️ Type-Safe**: Full TypeScript support
- **🎭 Animation Ready**: Framer Motion + Three.js integration
- **🗄️ ORM Included**: Auto CRUD generation with UI
- **🔐 Auth & RBAC**: Built-in authentication and role-based access
- **📊 Admin Panel**: Refine-style dashboard
- **🧪 Testing Ready**: Vitest + Playwright integration

## 📁 Project Structure

```
kilat/
├── apps/
│   └── web/                # Main application
│       ├── pages/         # SSR routes (Next.js style)
│       ├── app/           # App Router (modern)
│       ├── api/           # API routes
│       ├── admin/         # Admin dashboard
│       ├── components/    # Reusable components
│       └── styles/        # Tailwind + themes
│
├── core/                  # Framework modules
│   ├── kilatcore/        # Kernel & runtime
│   ├── kilatapi/         # API system
│   ├── kilatroute/       # Routing with RBAC
│   ├── kilatorm/         # ORM + CRUD generator
│   ├── kilatcss/         # Theme system
│   ├── kilatanim/        # Animation presets
│   ├── kilatplugin/      # Plugin architecture
│   ├── kilatpack/        # Build system
│   └── shared/           # Shared utilities
│
├── .kilat/               # Auto-generated metadata
├── kilat.config.ts       # Global configuration
└── kilat.cli.ts          # CLI entry point
```

## 🛠️ Core Modules

| Module | Description |
|--------|-------------|
| `kilatcore` | Kernel booting, lifecycle, Speedrun runtime |
| `kilatpack` | Fast build system (Turbopack alternative) |
| `kilatplugin` | Plugin system with auto-registration |
| `kilatapi` | REST & Server Actions with middleware |
| `kilatorm` | ORM + automatic CRUD UI generation |
| `kilatcss` | Tailwind wrapper + theme system |
| `kilatanim` | Framer Motion + Three.js presets |
| `kilatroute` | Advanced routing with RBAC & i18n |
| `kilatstate` | SSR-aware global state management |
| `kilatlink` | Router-aware links with prefetching |
| `kilatmeta` | SEO helpers & structured data |
| `kilatimage` | Built-in image optimization |
| `kilatsocket` | Real-time WebSocket handling |
| `kilatcache` | ISR/SWR caching layer |
| `kilatffmpeg` | Video/audio processing workers |

## 🎨 Built-in Themes

- **Glow** - Modern with subtle glows
- **Cyber** - Futuristic neon aesthetics  
- **Retro** - Vintage computing vibes
- **Pastel** - Soft, friendly colors
- **Comic** - Playful and vibrant
- **Semantic** - Clean, professional
- **Neon** - Bold, electric colors

## 🚀 Quick Start

```bash
# Install Bun (if not already installed)
curl -fsSL https://bun.sh/install | bash

# Create new Kilat.js project
bunx create-kilat my-app
cd my-app

# Start development server
bun dev

# Generate CRUD for a model
bun kilat generate crud User --ui

# Build for production
bun build

# Start production server
bun start
```

## 📖 CLI Commands

```bash
# Development
bun kilat dev              # Start dev server
bun kilat build            # Build for production
bun kilat start            # Start production server
bun kilat export           # Export static site

# Generators
bun kilat generate page About
bun kilat generate component Button
bun kilat generate api users
bun kilat generate crud Product --ui
bun kilat generate plugin analytics

# Database
bun kilat db migrate       # Run migrations
bun kilat db seed          # Seed database
bun kilat db studio        # Open database studio

# Testing
bun kilat test             # Run all tests
bun kilat test --unit      # Unit tests only
bun kilat test --e2e       # E2E tests only

# Plugins
bun kilat plugin list      # List plugins
bun kilat plugin install <name>
bun kilat plugin uninstall <name>

# Maintenance
bun kilat upgrade          # Upgrade framework
bun kilat info             # Environment info
```

## ⚙️ Configuration

Configure your app in `kilat.config.ts`:

```typescript
import { defineConfig } from '@kilat/core'

export default defineConfig({
  runtime: 'speedrun',
  css: {
    theme: 'cyber'
  },
  database: {
    provider: 'postgresql',
    url: process.env.DATABASE_URL
  },
  admin: {
    enabled: true,
    path: '/admin'
  },
  plugins: [
    // Your plugins here
  ]
})
```

## 🧩 Plugin Development

```typescript
import { definePlugin } from '@kilat/plugin'

export default definePlugin({
  name: 'my-plugin',
  version: '1.0.0',
  hooks: {
    onInit() {
      console.log('Plugin initialized!')
    },
    onBuild() {
      // Custom build logic
    }
  }
})
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

Inspired by Next.js, Nuxt, Remix, Refine, Vite, Laravel and other amazing frameworks.

---

**Built with ❤️ by the Kilat.js team**

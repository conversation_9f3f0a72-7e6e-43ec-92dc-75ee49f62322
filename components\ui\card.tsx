/**
 * KilatCard - Reusable card component with Kilat.js styling
 */

import { ReactNode, HTMLAttributes } from 'react'
import { cn } from '@/core/kilatlib/utils'

interface KilatCardProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode
  variant?: 'default' | 'glass' | 'glow'
  className?: string
}

const cardVariants = {
  default: 'bg-glow-surface border border-glow-surface',
  glass: 'kilat-glass',
  glow: 'bg-glow-surface border border-glow-primary/20 kilat-glow',
}

export function KilatCard({ 
  children, 
  variant = 'default', 
  className,
  ...props 
}: KilatCardProps) {
  return (
    <div
      className={cn(
        'rounded-lg shadow-kilat-soft transition-all duration-200 hover:shadow-lg',
        cardVariants[variant],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

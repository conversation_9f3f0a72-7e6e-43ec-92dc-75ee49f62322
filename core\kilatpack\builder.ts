/**
 * KilatPack - Internal build engine for Kilat.js
 * Standalone bundler without external dependencies
 */

import { readdir, readFile, writeFile, mkdir, stat } from 'fs/promises'
import { join, extname, dirname, basename } from 'path'
import { KilatConfig, BuildResult } from '../kilatcore/types'

export class KilatPack {
  private config: KilatConfig
  private startTime: number = 0
  
  constructor(config: KilatConfig) {
    this.config = config
  }
  
  /**
   * Build the application for production
   */
  async build(): Promise<BuildResult> {
    this.startTime = Date.now()
    console.log('📦 KilatPack: Starting build process...')
    
    const result: BuildResult = {
      success: false,
      errors: [],
      warnings: [],
      assets: [],
      duration: 0,
      size: 0
    }
    
    try {
      // Ensure output directory exists
      await this.ensureOutputDir()
      
      // Build steps
      await this.buildPages()
      await this.buildAPI()
      await this.buildAssets()
      await this.buildCSS()
      
      result.success = true
      result.duration = Date.now() - this.startTime
      
      console.log('✅ KilatPack: Build completed successfully!')
      
    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : String(error))
      console.error('❌ KilatPack: Build failed:', error)
    }
    
    return result
  }
  
  /**
   * Export to static files (SSG)
   */
  async exportStatic(): Promise<void> {
    console.log('📤 KilatPack: Exporting static files...')
    
    try {
      // Ensure export directory exists
      await this.ensureDir(this.config.export.outDir)
      
      // Generate static pages
      await this.generateStaticPages()
      
      // Copy assets
      await this.copyAssets()
      
      // Generate sitemap and robots.txt
      if (this.config.export.generateSitemap) {
        await this.generateSitemap()
      }
      
      if (this.config.export.generateRobots) {
        await this.generateRobots()
      }
      
      console.log('✅ KilatPack: Static export completed!')
      
    } catch (error) {
      console.error('❌ KilatPack: Export failed:', error)
      throw error
    }
  }
  
  /**
   * Ensure output directory exists
   */
  private async ensureOutputDir(): Promise<void> {
    await this.ensureDir(this.config.build.outDir)
  }
  
  /**
   * Ensure directory exists
   */
  private async ensureDir(dir: string): Promise<void> {
    try {
      await stat(dir)
    } catch {
      await mkdir(dir, { recursive: true })
    }
  }
  
  /**
   * Build pages
   */
  private async buildPages(): Promise<void> {
    console.log('🔨 Building pages...')
    
    const pagesDir = this.config.apps.dir
    await this.processPages(pagesDir)
  }
  
  /**
   * Process pages recursively
   */
  private async processPages(dir: string): Promise<void> {
    try {
      const entries = await readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = join(dir, entry.name)
        
        if (entry.isDirectory() && entry.name !== 'api') {
          await this.processPages(fullPath)
        } else if (entry.isFile()) {
          const ext = extname(entry.name)
          const name = basename(entry.name, ext)
          
          if (name === 'page' && this.config.apps.extensions.includes(ext)) {
            await this.buildPage(fullPath)
          }
        }
      }
    } catch (error) {
      console.error(`Error processing pages in ${dir}:`, error)
    }
  }
  
  /**
   * Build individual page
   */
  private async buildPage(pagePath: string): Promise<void> {
    try {
      const content = await readFile(pagePath, 'utf-8')
      
      // Simple transformation (in real implementation, this would be more complex)
      const transformed = await this.transformPage(content, pagePath)
      
      // Write to output directory
      const relativePath = pagePath.replace(this.config.apps.dir, '')
      const outputPath = join(this.config.build.outDir, relativePath)
      
      await this.ensureDir(dirname(outputPath))
      await writeFile(outputPath, transformed)
      
    } catch (error) {
      console.error(`Error building page ${pagePath}:`, error)
    }
  }
  
  /**
   * Transform page content
   */
  private async transformPage(content: string, pagePath: string): Promise<string> {
    // Placeholder transformation
    // In real implementation, this would:
    // - Parse TSX/JSX
    // - Transform imports
    // - Bundle dependencies
    // - Optimize code
    // - Generate HTML
    
    return content
  }
  
  /**
   * Build API routes
   */
  private async buildAPI(): Promise<void> {
    console.log('🔨 Building API routes...')
    
    const apiDir = join(this.config.apps.dir, 'api')
    await this.processAPI(apiDir)
  }
  
  /**
   * Process API routes
   */
  private async processAPI(dir: string): Promise<void> {
    try {
      const entries = await readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = join(dir, entry.name)
        
        if (entry.isDirectory()) {
          await this.processAPI(fullPath)
        } else if (entry.isFile()) {
          const ext = extname(entry.name)
          const name = basename(entry.name, ext)
          
          if (name === 'route' && this.config.apps.extensions.includes(ext)) {
            await this.buildAPIRoute(fullPath)
          }
        }
      }
    } catch (error) {
      console.error(`Error processing API in ${dir}:`, error)
    }
  }
  
  /**
   * Build individual API route
   */
  private async buildAPIRoute(routePath: string): Promise<void> {
    try {
      const content = await readFile(routePath, 'utf-8')
      
      // Transform API route
      const transformed = await this.transformAPI(content, routePath)
      
      // Write to output directory
      const relativePath = routePath.replace(this.config.apps.dir, '')
      const outputPath = join(this.config.build.outDir, relativePath)
      
      await this.ensureDir(dirname(outputPath))
      await writeFile(outputPath, transformed)
      
    } catch (error) {
      console.error(`Error building API route ${routePath}:`, error)
    }
  }
  
  /**
   * Transform API content
   */
  private async transformAPI(content: string, routePath: string): Promise<string> {
    // Placeholder transformation
    return content
  }
  
  /**
   * Build assets (CSS, images, etc.)
   */
  private async buildAssets(): Promise<void> {
    console.log('🔨 Building assets...')
    
    // Copy public assets
    await this.copyPublicAssets()
  }
  
  /**
   * Copy public assets
   */
  private async copyPublicAssets(): Promise<void> {
    const publicDir = 'public'
    const outputPublicDir = join(this.config.build.outDir, 'public')
    
    try {
      await this.copyDirectory(publicDir, outputPublicDir)
    } catch (error) {
      console.error('Error copying public assets:', error)
    }
  }
  
  /**
   * Copy directory recursively
   */
  private async copyDirectory(src: string, dest: string): Promise<void> {
    try {
      await this.ensureDir(dest)
      const entries = await readdir(src, { withFileTypes: true })
      
      for (const entry of entries) {
        const srcPath = join(src, entry.name)
        const destPath = join(dest, entry.name)
        
        if (entry.isDirectory()) {
          await this.copyDirectory(srcPath, destPath)
        } else {
          const content = await readFile(srcPath)
          await writeFile(destPath, content)
        }
      }
    } catch (error) {
      // Directory might not exist, which is fine
    }
  }
  
  /**
   * Build CSS
   */
  private async buildCSS(): Promise<void> {
    console.log('🔨 Building CSS...')
    
    // Process Tailwind CSS and custom styles
    // This would integrate with the KilatCSS system
  }
  
  /**
   * Generate static pages for export
   */
  private async generateStaticPages(): Promise<void> {
    // Generate HTML files for each route
    // This would render React components to HTML
  }
  
  /**
   * Copy assets for export
   */
  private async copyAssets(): Promise<void> {
    const buildDir = this.config.build.outDir
    const exportDir = this.config.export.outDir
    
    await this.copyDirectory(buildDir, exportDir)
  }
  
  /**
   * Generate sitemap.xml
   */
  private async generateSitemap(): Promise<void> {
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://example.com/</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <priority>1.0</priority>
  </url>
</urlset>`
    
    const sitemapPath = join(this.config.export.outDir, 'sitemap.xml')
    await writeFile(sitemapPath, sitemap)
  }
  
  /**
   * Generate robots.txt
   */
  private async generateRobots(): Promise<void> {
    const robots = `User-agent: *
Allow: /

Sitemap: https://example.com/sitemap.xml`
    
    const robotsPath = join(this.config.export.outDir, 'robots.txt')
    await writeFile(robotsPath, robots)
  }
}

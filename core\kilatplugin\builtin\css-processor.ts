POSISIKAN DIRIMU SEBAGAI:
Lead Framework Engineer + DX Specialist
<PERSON><PERSON><PERSON> adalah merancang dan mengimplementasikan <PERSON>.js, sebuah framework fullstack modern dan mandiri, dibangun dari nol — tanpa mengandalkan framework lain. Kilat.js memadukan kekuatan:
Bun.js + Node.js sebagai runtime utama (SpeedRun)
App Mapping Monolith sebagai struktur file-based routing modern
KilatPack build engine internal super cepat
KilatPlugin sistem plugin modular mirip Vite
KilatAPI handler RESTful/ServerAction bawaan
KilatService untuk business logic terpisah dari view
KilatCSS & KilatAnims sebagai sistem tema dan animasi

🧱 TAHAP 1: STRUKTUR DASAR — APP MAPPING MONOLITH (STANDALONE)

kilat/
├── apps/                      # 🔀 File-based router (Apps Mapping)
│   ├── layout.tsx            # Global layout
│   ├── page.tsx              # Homepage (/)
│   ├── about/                # /about
│   │   └── page.tsx
│   ├── dashboard/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   ├── settings/
│   │   │   └── page.tsx
│   │   └── users/
│   │       └── page.tsx
│   └── api/                  # 🛠️ Native API handler
│       ├── auth/route.ts
│       └── users/route.ts
│
├── components/               # 💠 Global UI components
│   ├── ui/
│   ├── layout/
│   └── shared/
│
├── public/                   # 🖼️ Static files (logo, font, gambar)
│   ├── logo.png
│   └── favicon.ico
│
├── core/                     # ⚙️ Kernel modular internal
│   ├── kilatcore/            # Lifecycle & runtime
│   ├── kilatpack/            # Build engine mandiri
│   ├── kilatplugin/          # Plugin loader dev/build
│   ├── kilatservice/         # 💡 Business logic (auth, user, dsb)
│   │   ├── auth.service.ts
│   │   └── user.service.ts
│   ├── kilatorm/             # ORM ringan + auto CRUD
│   ├── kilatcss/             # Tailwind + theme system
│   │   ├── globals.css
│   │   ├── tailwind.config.ts
│   │   └── themes/
│   │       ├── glow.ts
│   │       └── cyber.ts
│   ├── kilatanim/            # Preset animasi + Three.js
│   ├── kilatstate/           # Global SSR-aware state
│   ├── kilatmeta/            # SEO, OpenGraph, JSON-LD
│   ├── kilatimage/           # Optimizer gambar internal
│   ├── kilatsocket/          # WebSocket native
│   ├── kilatcache/           # Caching ISR/SWR
│   ├── kilatffmpeg/          # Async encoder video/audio
│   ├── kilatfonts/           # Font loader
│   ├── kilatlib/             # ✨ Utils, validator, constants
│   ├── kilatupgrade/         # Auto updater CLI
│   ├── kilaterror/           # Modular error overlay
│   └── shared/               # Common helpers antar modul
│
├── middleware.ts             # 🛡️ Global middleware: auth, RBAC, ACL
├── kilat.config.js           # ⚙️ Konfigurasi utama Kilat.js
├── kilatcss.config.js        # 🎨 Tema & styling preset
├── kilat.cli.ts              # 🔧 CLI generator mandiri
├── .kilat/                   # Runtime metadata internal
├── tsconfig.json
├── readme.md
├── struktur.md
└── package.json


⚙️ TAHAP 2: SPEEDRUN RUNTIME – NATIVE SERVER TANPA EXPRESS
Kilat.js berjalan 100% mandiri dengan runtime bernama SpeedRun:
🧠 Dibangun di atas Bun.js + Node.Js + native HTTP server
🔁 Fallback opsional ke Node.js hanya jika bun tidak tersedia
🌀 Mendukung SSR, SSG, CSR, ISR
🔀 Auto router loader (apps/)
🔌 Auto plugin loader (core/kilatplugin)
🚦 Middleware processor (middleware.ts)
🔧 Zero dependency pada Express, Vite, Next, dsb

🎨 TAHAP 3: UI & THEME SYSTEM
core/kilatcss/ → Wrapper Tailwind + preset theme (glow, cyber, pastel, retro)
core/kilatanim/ → Preset animasi intro/outro, scroll, 3D orbit
components/ui/ → Bisa pakai Shadcn UI atau UI native Kilat

🧪 TAHAP 4: DX SYSTEM (Developer Experience)
Fitur DX
Keterangan
🔧 kilat generate
Scaffold page, api, plugin, layout
📦 kilat build
Build dengan kilatpack internal
🚀 kilat dev
Dev server (hot reload native)
🧩 Plugin loader
Auto register plugin mirip Vite
🧪 Testing
Vitest (unit) + Playwright (e2e)
🏝️ Island Architecture
Auto partial hydration
📘 Docs generator
Berdasarkan metadata plugin & config

🔐 TAHAP 5: MIDDLEWARE & API LAYER
middleware.ts: Global intercept layer
apps/api/: File-based native handler ala /api/auth/route.ts
kilatservice/: Reusable business logic terpisah dari view/API
Contoh middleware.ts:

import { isAuthenticated } from "@/core/kilatlib/auth"

export async function middleware(req) {
  if (req.url.startsWith("/dashboard")) {
    const session = await isAuthenticated(req)
    if (!session) return Response.redirect("/login")
  }
  return NextResponse.next()
}


🧑‍💻 TAHAP 6: EKSEKUSI PENGEMBANGAN
npm create kilat-app → Buat starter Kilat.js
Edit kilat.config.js, tailwind.config.ts, dll
Buat halaman: kilat generate page about
Buat API: kilat generate api users
Tambahkan service: core/kilatservice/user.service.ts
Jalankan dev: bun kilat.cli.ts dev → Live reload via Bun
Build: bun kilat.cli.ts build → Bundling dengan kilatpack
Export ke static/SSG: bun kilat.cli.ts export

✅ TAHAP 7: SIAP PRODUKSI
Mode Output
Siap?
Catatan
SSR
✅
Built-in via Bun-native HTTP server
CSR
✅
Bisa di-render di client
SSG
✅
kilat export support
ISR
✅
Via kilatcache module

🧠 KESIMPULAN
Kilat.js adalah:
✅ Framework fullstack mandiri ✅ Berjalan tanpa Next/Vite/Express ✅ Runtime internal sendiri (SpeedRun) ✅ App Mapping Modern + Plugin Modular ✅ UI, API, Service, State, ORM, CLI, Middleware — all built-in


 * CSS Processor Plugin - Built-in plugin for CSS processing
 * Handles Tailwind CSS compilation, PostCSS processing, and CSS optimization
 */

import { readFile, writeFile } from 'fs/promises'
import { join, dirname } from 'path'
import { Plugin, BuildContext } from '../../kilatcore/types'

export const cssProcessorPlugin: Plugin = {
  name: 'css-processor',
  
  async setup(build: BuildContext) {
    console.log('🎨 CSS Processor plugin initialized')
    
    // Handle CSS file processing
    build.onLoad(/\.css$/, async (args) => {
      const content = await readFile(args.path, 'utf-8')
      
      let processedContent = content
      
      // Process Tailwind CSS directives
      if (content.includes('@tailwind')) {
        processedContent = await processTailwindCSS(content, build)
      }
      
      // Process PostCSS if needed
      processedContent = await processPostCSS(processedContent, build)
      
      // Minify in production
      if (build.isProd) {
        processedContent = minifyCSS(processedContent)
      }
      
      return {
        contents: processedContent,
        loader: 'css'
      }
    })
    
    // Handle SCSS/Sass files
    build.onLoad(/\.(scss|sass)$/, async (args) => {
      const content = await readFile(args.path, 'utf-8')
      
      // Compile SCSS to CSS (simplified)
      let compiledCSS = await compileSCSS(content, args.path)
      
      // Process like regular CSS
      if (build.isProd) {
        compiledCSS = minifyCSS(compiledCSS)
      }
      
      return {
        contents: compiledCSS,
        loader: 'css'
      }
    })
    
    // Handle CSS modules
    build.onLoad(/\.module\.css$/, async (args) => {
      const content = await readFile(args.path, 'utf-8')
      
      const { css, classNames } = await processCSSModules(content, args.path)
      
      return {
        contents: `
          const styles = ${JSON.stringify(classNames)};
          export default styles;
        `,
        loader: 'js'
      }
    })
  }
}

/**
 * Process Tailwind CSS directives
 */
async function processTailwindCSS(content: string, build: BuildContext): Promise<string> {
  console.log('🌊 Processing Tailwind CSS...')
  
  // This is a simplified Tailwind processing
  // In a real implementation, you'd use the actual Tailwind CSS compiler
  
  let processed = content
  
  // Replace @tailwind directives with actual CSS
  processed = processed.replace('@tailwind base;', getTailwindBase())
  processed = processed.replace('@tailwind components;', getTailwindComponents())
  processed = processed.replace('@tailwind utilities;', getTailwindUtilities())
  
  return processed
}

/**
 * Process PostCSS
 */
async function processPostCSS(content: string, build: BuildContext): Promise<string> {
  // Simplified PostCSS processing
  // In a real implementation, you'd use the actual PostCSS processor
  
  let processed = content
  
  // Autoprefixer simulation
  processed = addVendorPrefixes(processed)
  
  // CSS nesting support
  processed = processNestedCSS(processed)
  
  return processed
}

/**
 * Compile SCSS to CSS
 */
async function compileSCSS(content: string, filePath: string): Promise<string> {
  console.log('💎 Compiling SCSS...')
  
  // This is a very simplified SCSS compilation
  // In a real implementation, you'd use sass or node-sass
  
  let compiled = content
  
  // Process variables (very basic)
  compiled = processSCSSVariables(compiled)
  
  // Process nesting (very basic)
  compiled = processSCSSNesting(compiled)
  
  // Process mixins (very basic)
  compiled = processSCSSMixins(compiled)
  
  return compiled
}

/**
 * Process CSS Modules
 */
async function processCSSModules(content: string, filePath: string): Promise<{ css: string, classNames: Record<string, string> }> {
  console.log('📦 Processing CSS Modules...')
  
  const classNames: Record<string, string> = {}
  let css = content
  
  // Extract class names and generate unique identifiers
  const classRegex = /\.([a-zA-Z_-][a-zA-Z0-9_-]*)/g
  let match
  
  while ((match = classRegex.exec(content)) !== null) {
    const originalClass = match[1]
    const hashedClass = generateHashedClassName(originalClass, filePath)
    
    classNames[originalClass] = hashedClass
    css = css.replace(new RegExp(`\\.${originalClass}\\b`, 'g'), `.${hashedClass}`)
  }
  
  return { css, classNames }
}

/**
 * Minify CSS
 */
function minifyCSS(content: string): string {
  return content
    // Remove comments
    .replace(/\/\*[\s\S]*?\*\//g, '')
    // Remove unnecessary whitespace
    .replace(/\s+/g, ' ')
    // Remove whitespace around certain characters
    .replace(/\s*([{}:;,>+~])\s*/g, '$1')
    // Remove trailing semicolons
    .replace(/;}/g, '}')
    // Remove leading/trailing whitespace
    .trim()
}

/**
 * Add vendor prefixes
 */
function addVendorPrefixes(content: string): string {
  // Simplified autoprefixer
  const prefixMap: Record<string, string[]> = {
    'transform': ['-webkit-transform', '-moz-transform', '-ms-transform'],
    'transition': ['-webkit-transition', '-moz-transition', '-ms-transition'],
    'border-radius': ['-webkit-border-radius', '-moz-border-radius'],
    'box-shadow': ['-webkit-box-shadow', '-moz-box-shadow'],
    'user-select': ['-webkit-user-select', '-moz-user-select', '-ms-user-select'],
  }
  
  let processed = content
  
  for (const [property, prefixes] of Object.entries(prefixMap)) {
    const regex = new RegExp(`(\\s|^)${property}\\s*:`, 'g')
    processed = processed.replace(regex, (match) => {
      const prefixedProperties = prefixes.map(prefix => match.replace(property, prefix)).join('\n  ')
      return prefixedProperties + '\n  ' + match
    })
  }
  
  return processed
}

/**
 * Process nested CSS
 */
function processNestedCSS(content: string): string {
  // Very basic CSS nesting support
  // In a real implementation, you'd use a proper CSS parser
  return content
}

/**
 * Process SCSS variables
 */
function processSCSSVariables(content: string): string {
  const variables: Record<string, string> = {}
  
  // Extract variables
  const variableRegex = /\$([a-zA-Z_-][a-zA-Z0-9_-]*)\s*:\s*([^;]+);/g
  let match
  
  while ((match = variableRegex.exec(content)) !== null) {
    variables[match[1]] = match[2].trim()
  }
  
  // Replace variable usage
  let processed = content
  for (const [name, value] of Object.entries(variables)) {
    const regex = new RegExp(`\\$${name}\\b`, 'g')
    processed = processed.replace(regex, value)
  }
  
  // Remove variable declarations
  processed = processed.replace(/\$[a-zA-Z_-][a-zA-Z0-9_-]*\s*:\s*[^;]+;/g, '')
  
  return processed
}

/**
 * Process SCSS nesting
 */
function processSCSSNesting(content: string): string {
  // Very basic nesting support
  // In a real implementation, you'd use a proper SCSS parser
  return content
}

/**
 * Process SCSS mixins
 */
function processSCSSMixins(content: string): string {
  // Very basic mixin support
  // In a real implementation, you'd use a proper SCSS parser
  return content
}

/**
 * Generate hashed class name for CSS Modules
 */
function generateHashedClassName(originalClass: string, filePath: string): string {
  // Simple hash generation
  const hash = Buffer.from(filePath + originalClass).toString('base64').slice(0, 8).replace(/[^a-zA-Z0-9]/g, '')
  return `${originalClass}_${hash}`
}

/**
 * Get Tailwind base styles
 */
function getTailwindBase(): string {
  return `
/* Tailwind CSS Base Styles */
*,
::before,
::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

body {
  margin: 0;
  line-height: inherit;
}
`
}

/**
 * Get Tailwind component styles
 */
function getTailwindComponents(): string {
  return `
/* Tailwind CSS Components */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}
`
}

/**
 * Get Tailwind utility styles
 */
function getTailwindUtilities(): string {
  return `
/* Tailwind CSS Utilities */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

.text-white { color: #ffffff; }
.text-black { color: #000000; }
.text-gray-500 { color: #6b7280; }

.bg-white { background-color: #ffffff; }
.bg-black { background-color: #000000; }
.bg-gray-100 { background-color: #f3f4f6; }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.m-4 { margin: 1rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }
`
}

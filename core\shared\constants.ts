// Constants for Kilat.js Framework

export const KILAT_VERSION = '0.1.0'

export const DEFAULT_PORT = 3000
export const DEFAULT_HOST = 'localhost'

export const SUPPORTED_RUNTIMES = ['speedrun', 'bun', 'node'] as const
export const SUPPORTED_MODES = ['development', 'production', 'test'] as const

export const ROUTING_STRATEGIES = ['pages', 'app', 'hybrid'] as const

export const CSS_FRAMEWORKS = ['tailwind', 'styled-components', 'emotion'] as const

export const THEMES = [
  'glow',
  'cyber', 
  'retro',
  'pastel',
  'comic',
  'semantic',
  'neon'
] as const

export const ANIMATION_FRAMEWORKS = ['framer-motion', 'react-spring', 'lottie'] as const

export const DATABASE_PROVIDERS = ['sqlite', 'postgresql', 'mysql', 'mongodb'] as const

export const CACHE_STRATEGIES = ['isr', 'swr', 'static'] as const

export const AUTH_PROVIDERS = ['jwt', 'oauth', 'session'] as const

export const IMAGE_FORMATS = ['webp', 'avif', 'jpeg', 'png'] as const

export const VIDEO_FORMATS = ['mp4', 'webm', 'avi', 'mov'] as const
export const AUDIO_FORMATS = ['mp3', 'wav', 'ogg', 'aac'] as const

export const HTTP_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'] as const

// File extensions
export const SCRIPT_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'] as const
export const STYLE_EXTENSIONS = ['.css', '.scss', '.sass', '.less'] as const
export const ASSET_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.avif'] as const

// Default configurations
export const DEFAULT_BUILD_CONFIG = {
  target: 'es2022',
  outDir: 'dist',
  sourcemap: true,
  minify: false,
  splitting: true,
  treeshaking: true,
  externals: [],
  plugins: []
}

export const DEFAULT_SERVER_CONFIG = {
  port: DEFAULT_PORT,
  host: DEFAULT_HOST,
  https: false,
  cors: true,
  compression: true,
  middleware: []
}

export const DEFAULT_ROUTING_CONFIG = {
  strategy: 'hybrid' as const,
  basePath: '',
  trailingSlash: false,
  caseSensitive: false,
  i18n: {
    enabled: false,
    locales: ['en'],
    defaultLocale: 'en'
  },
  rbac: {
    enabled: false,
    roles: [],
    permissions: []
  }
}

export const DEFAULT_CSS_CONFIG = {
  framework: 'tailwind' as const,
  theme: 'glow' as const,
  customThemes: {},
  purge: true,
  autoprefixer: true
}

export const DEFAULT_ANIMATION_CONFIG = {
  framework: 'framer-motion' as const,
  presets: ['intro', 'orbit', '3d'],
  three: {
    enabled: true,
    physics: false
  }
}

export const DEFAULT_DATABASE_CONFIG = {
  provider: 'sqlite' as const,
  url: 'file:./dev.db',
  migrations: './prisma/migrations',
  seed: './prisma/seed.ts',
  crud: {
    autoGenerate: true,
    ui: true
  }
}

export const DEFAULT_API_CONFIG = {
  prefix: '/api',
  cors: true,
  rateLimit: {
    enabled: false,
    max: 100,
    windowMs: 15 * 60 * 1000
  },
  auth: {
    enabled: false,
    provider: 'jwt' as const
  }
}

export const DEFAULT_CACHE_CONFIG = {
  strategy: 'isr' as const,
  ttl: 3600,
  redis: {
    enabled: false
  }
}

export const DEFAULT_IMAGE_CONFIG = {
  domains: [],
  formats: ['webp', 'avif'],
  sizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
  quality: 75,
  lazy: true
}

export const DEFAULT_SEO_CONFIG = {
  sitemap: true,
  robots: true,
  openGraph: true,
  jsonLd: true,
  meta: {
    title: 'Kilat.js App',
    description: 'Built with Kilat.js Framework',
    keywords: []
  }
}

export const DEFAULT_DEV_CONFIG = {
  overlay: true,
  hotReload: true,
  errorBoundary: true,
  devtools: true
}

// Error codes
export const ERROR_CODES = {
  CONFIG_INVALID: 'CONFIG_INVALID',
  RUNTIME_ERROR: 'RUNTIME_ERROR',
  BUILD_ERROR: 'BUILD_ERROR',
  PLUGIN_ERROR: 'PLUGIN_ERROR',
  ROUTE_ERROR: 'ROUTE_ERROR',
  API_ERROR: 'API_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  AUTH_ERROR: 'AUTH_ERROR'
} as const

// Plugin hooks
export const PLUGIN_HOOKS = [
  'onInit',
  'onBuild', 
  'onDev',
  'onDestroy'
] as const

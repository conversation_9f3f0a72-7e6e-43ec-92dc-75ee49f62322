#!/usr/bin/env bun
/**
 * Ki<PERSON>.js CLI - Command Line Interface
 */

import { Command } from 'commander'
import { KILAT_VERSION } from './core/shared/constants'
import { logger } from './core/shared/logger'

const program = new Command()

program
  .name('kilat')
  .description('Kilat.js Framework CLI')
  .version(KILAT_VERSION)

// Development commands
program
  .command('dev')
  .description('Start development server')
  .option('-p, --port <port>', 'Port number', '3000')
  .option('-h, --host <host>', 'Host address', 'localhost')
  .action(async (options) => {
    const { dev } = await import('./core/kilat/dev')
    await dev(options)
  })

program
  .command('build')
  .description('Build for production')
  .option('-o, --output <dir>', 'Output directory', 'dist')
  .option('--analyze', 'Analyze bundle size')
  .action(async (options) => {
    const { build } = await import('./core/kilat/pack/build')
    await build(options)
  })

program
  .command('start')
  .description('Start production server')
  .option('-p, --port <port>', 'Port number', '3000')
  .action(async (options) => {
    const { start } = await import('./core/kilat/start')
    await start(options)
  })

program
  .command('export')
  .description('Export static site')
  .option('-o, --output <dir>', 'Output directory', 'out')
  .action(async (options) => {
    const { exportStatic } = await import('./core/kilat/pack/export')
    await exportStatic(options)
  })

// Generator commands
const generate = program
  .command('generate')
  .alias('g')
  .description('Generate code')

generate
  .command('page <name>')
  .description('Generate a new page')
  .option('-t, --template <template>', 'Page template', 'default')
  .action(async (name, options) => {
    const { generatePage } = await import('./core/kilat/cli/generators')
    await generatePage({ name, ...options })
  })

generate
  .command('component <name>')
  .description('Generate a new component')
  .option('-t, --template <template>', 'Component template', 'default')
  .action(async (name, options) => {
    const { generateComponent } = await import('./core/kilat/cli/generators')
    await generateComponent({ name, ...options })
  })

generate
  .command('api <name>')
  .description('Generate API route')
  .option('-m, --method <method>', 'HTTP method', 'GET')
  .action(async (name, options) => {
    const { generateAPI } = await import('./core/kilat/cli/generators')
    await generateAPI({ name, ...options })
  })

generate
  .command('crud <model>')
  .description('Generate CRUD operations for model')
  .option('--ui', 'Generate UI components')
  .action(async (model, options) => {
    const { generateAPI } = await import('./core/kilat/cli/generators')
    await generateAPI({ name: model, ...options })
  })

generate
  .command('plugin <name>')
  .description('Generate a new plugin')
  .action(async (name) => {
    const { generateAPI } = await import('./core/kilat/cli/generators')
    await generateAPI({ name })
  })

// Database commands
const db = program
  .command('db')
  .description('Database operations')

db
  .command('migrate')
  .description('Run database migrations')
  .action(async () => {
    const { migrate } = await import('./core/kilat/orm/migrate')
    await migrate()
  })

db
  .command('seed')
  .description('Seed database')
  .action(async () => {
    const { seed } = await import('./core/kilat/orm/seed')
    await seed()
  })

db
  .command('studio')
  .description('Open database studio')
  .action(async () => {
    const { studio } = await import('./core/kilat/orm/studio')
    await studio()
  })

// Testing commands
program
  .command('test')
  .description('Run tests')
  .option('--unit', 'Run unit tests only')
  .option('--e2e', 'Run e2e tests only')
  .option('--coverage', 'Generate coverage report')
  .action(async (options) => {
    const { test } = await import('./core/kilat/test')
    await test(options)
  })

// Plugin commands
const plugin = program
  .command('plugin')
  .description('Plugin management')

plugin
  .command('list')
  .description('List installed plugins')
  .action(async () => {
    const { listPlugins } = await import('./core/kilat/plugin/manager')
    listPlugins()
  })

plugin
  .command('install <name>')
  .description('Install a plugin')
  .action(async (name) => {
    const { installPlugin } = await import('./core/kilat/plugin/manager')
    await installPlugin(name)
  })

plugin
  .command('uninstall <name>')
  .description('Uninstall a plugin')
  .action(async (name) => {
    const { uninstallPlugin } = await import('./core/kilat/plugin/manager')
    await uninstallPlugin(name)
  })

// Upgrade command
program
  .command('upgrade')
  .description('Upgrade Kilat.js framework')
  .option('--check', 'Check for updates only')
  .action(async (options) => {
    const { upgrade } = await import('./core/kilat/upgrade')
    await upgrade(options)
  })

// Info command
program
  .command('info')
  .description('Display environment information')
  .action(async () => {
    const { info } = await import('./core/kilat/info')
    await info()
  })

// Error handling
program.exitOverride()

try {
  program.parse(process.argv)
} catch (error: any) {
  if (error.code === 'commander.help') {
    process.exit(0)
  }
  logger.error('CLI Error:', error.message)
  process.exit(1)
}

export { program }
